<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="bloomg_flutter by Algomash">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Bloomg Flutter">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>Bloomg Flutter</title>
  <link rel="manifest" href="manifest.json">

  <!-- Google Sign-In Web Configuration -->
  <meta name="google-signin-client_id"
    content="1068175978703-asn9ene1hjta4lhro0ni4v0ji8l40tha.apps.googleusercontent.com">

  <!-- Apple Sign-In Web Configuration -->
  <meta name="appleid-signin-client-id" content="com.algomash.radiance">
  <meta name="appleid-signin-scope" content="name email">
  <meta name="appleid-signin-redirect-uri" content="https://bloomg-flutter.firebaseapp.com/__/auth/handler">
  <meta name="appleid-signin-state" content="origin:web">
  <meta name="appleid-signin-use-popup" content="true">

  <!-- Apple Sign-In JavaScript SDK -->
  <script type="text/javascript"
    src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
</head>

<body>
  <script>
    window.addEventListener('load', function (ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: "{{flutter_service_worker_version}}",
        },
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
  <script src="flutter.js" defer></script>
</body>

</html>