# Fastfile for Bloomg Flutter Apple Sign-In Configuration
# This file contains all the automation lanes for Apple Sign-In setup

default_platform(:ios)

# Import helper methods
import "fastlane/helpers/apple_signin_helper.rb"

# Global variables
APPLE_SIGNIN_SERVICE_ID = "com.algomash.radiance"
FIREBASE_PROJECT_ID = "bloomg-flutter"

platform :ios do
  
  # ============================================================================
  # APPLE SIGN-IN CONFIGURATION LANES
  # ============================================================================
  
  desc "Configure Apple Sign-In for all environments"
  lane :setup_apple_signin do |options|
    environment = options[:environment] || "production"
    
    UI.header "🍎 Setting up Apple Sign-In for #{environment} environment"
    
    # Validate environment
    validate_environment(environment)
    
    # Setup App Store Connect API key
    setup_app_store_connect_api
    
    # Configure bundle ID and capabilities
    configure_bundle_id_capabilities(environment: environment)
    
    # Update iOS project capabilities
    enable_ios_apple_signin_capability(environment: environment)
    
    # Validate configuration
    validate_apple_signin_setup(environment: environment)
    
    UI.success "✅ Apple Sign-In setup completed for #{environment}"
  end
  
  desc "Setup Apple Sign-In for all environments (production, staging, development)"
  lane :setup_apple_signin_all_environments do
    ["production", "staging", "development"].each do |env|
      setup_apple_signin(environment: env)
    end
    
    UI.success "✅ Apple Sign-In setup completed for all environments"
  end
  
  desc "Enable Apple Sign-In capability in iOS project"
  lane :enable_ios_apple_signin_capability do |options|
    environment = options[:environment] || "production"
    bundle_id = get_bundle_id_for_environment(environment)
    
    UI.message "📱 Enabling Apple Sign-In capability for #{bundle_id}"
    
    # Update Xcode project capabilities
    update_project_provisioning(
      xcodeproj: "ios/Runner.xcodeproj",
      target_filter: "Runner",
      build_configuration: get_build_configuration(environment),
      certificate_type: "development"
    )
    
    # Ensure entitlements file has Apple Sign-In capability
    ensure_apple_signin_entitlement(environment: environment)
    
    UI.success "✅ iOS Apple Sign-In capability enabled"
  end
  
  # ============================================================================
  # APPLE DEVELOPER PORTAL INTEGRATION
  # ============================================================================
  
  desc "Configure bundle ID and capabilities in Apple Developer Portal"
  lane :configure_bundle_id_capabilities do |options|
    environment = options[:environment] || "production"
    bundle_id = get_bundle_id_for_environment(environment)
    
    UI.message "🔧 Configuring bundle ID capabilities for #{bundle_id}"
    
    # Register bundle ID if it doesn't exist
    register_bundle_id_if_needed(
      bundle_id: bundle_id,
      name: "Bloomg Flutter #{environment.capitalize}"
    )
    
    # Enable Apple Sign-In capability
    enable_apple_signin_capability(bundle_id: bundle_id)
    
    # Update provisioning profiles
    update_provisioning_profiles(
      bundle_id: bundle_id,
      environment: environment
    )
    
    UI.success "✅ Bundle ID capabilities configured"
  end
  
  desc "Register bundle ID in Apple Developer Portal if needed"
  lane :register_bundle_id_if_needed do |options|
    bundle_id = options[:bundle_id]
    name = options[:name]
    
    begin
      # Check if bundle ID exists
      app_store_connect_api_key
      
      # This will create the bundle ID if it doesn't exist
      produce(
        app_identifier: bundle_id,
        app_name: name,
        skip_itc: true,
        skip_devcenter: false
      )
      
      UI.success "✅ Bundle ID #{bundle_id} registered/verified"
    rescue => ex
      UI.error "❌ Failed to register bundle ID: #{ex.message}"
      raise ex
    end
  end
  
  # ============================================================================
  # CROSS-PLATFORM CONFIGURATION
  # ============================================================================
  
  desc "Configure Apple Sign-In for web platform"
  lane :configure_web_apple_signin do
    UI.header "🌐 Configuring Apple Sign-In for Web platform"
    
    # Validate Firebase configuration
    validate_firebase_configuration
    
    # Configure Apple Sign-In service ID for web
    configure_apple_signin_service_id
    
    # Update web configuration files
    update_web_configuration
    
    UI.success "✅ Web Apple Sign-In configuration completed"
  end
  
  desc "Configure Apple Sign-In for Android platform"
  lane :configure_android_apple_signin do
    UI.header "🤖 Configuring Apple Sign-In for Android platform"
    
    # Validate Android configuration
    validate_android_configuration
    
    # Update Android manifest and configuration
    update_android_configuration
    
    UI.success "✅ Android Apple Sign-In configuration completed"
  end
  
  # ============================================================================
  # VALIDATION AND TESTING
  # ============================================================================
  
  desc "Validate Apple Sign-In setup for all platforms"
  lane :validate_apple_signin_setup do |options|
    environment = options[:environment] || "production"
    
    UI.header "🔍 Validating Apple Sign-In setup for #{environment}"
    
    # Validate iOS configuration
    validate_ios_configuration(environment: environment)
    
    # Validate Firebase configuration
    validate_firebase_configuration
    
    # Validate Apple Developer Portal configuration
    validate_developer_portal_configuration(environment: environment)
    
    UI.success "✅ Apple Sign-In validation completed"
  end
  
  desc "Run Apple Sign-In integration tests"
  lane :test_apple_signin do |options|
    environment = options[:environment] || "development"
    
    UI.header "🧪 Running Apple Sign-In integration tests"
    
    # Build the app for testing
    build_for_testing(environment: environment)
    
    # Run Playwright tests for web authentication
    run_web_authentication_tests
    
    # Run iOS simulator tests
    run_ios_simulator_tests(environment: environment)
    
    UI.success "✅ Apple Sign-In tests completed"
  end
  
  # ============================================================================
  # BUILD AND DEPLOYMENT LANES
  # ============================================================================

  desc "Build app for testing with Apple Sign-In"
  lane :build_for_testing do |options|
    environment = options[:environment] || "development"

    UI.header "🔨 Building app for testing (#{environment})"

    # Clean build
    clean_apple_signin_config

    # Setup certificates and provisioning profiles
    setup_certificates_and_profiles(environment: environment)

    # Build the app
    build_app(
      scheme: "Runner",
      configuration: get_build_configuration(environment),
      export_method: "development",
      output_directory: "./build/ios",
      output_name: "Runner_#{environment}.ipa"
    )

    UI.success "✅ Build completed for #{environment}"
  end

  desc "Setup certificates and provisioning profiles"
  lane :setup_certificates_and_profiles do |options|
    environment = options[:environment] || "development"
    bundle_id = get_bundle_id_for_environment(environment)

    UI.message "📋 Setting up certificates and profiles for #{bundle_id}"

    # Setup App Store Connect API
    setup_app_store_connect_api

    # Get certificates
    cert(
      development: environment == "development",
      output_path: "./certificates"
    )

    # Get provisioning profiles
    sigh(
      app_identifier: bundle_id,
      development: environment == "development",
      force: true,
      output_path: "./profiles"
    )

    UI.success "✅ Certificates and profiles setup completed"
  end

  desc "Run web authentication tests using Playwright"
  lane :run_web_authentication_tests do
    UI.header "🌐 Running web authentication tests"

    begin
      # Ensure Playwright is installed
      sh("cd .. && npm install playwright")

      # Run Apple Sign-In web tests
      sh("cd .. && npx playwright test tests/apple_signin_web_test.spec.js")

      UI.success "✅ Web authentication tests passed"
    rescue => ex
      UI.error "❌ Web authentication tests failed: #{ex.message}"
      raise ex
    end
  end

  desc "Run iOS simulator tests"
  lane :run_ios_simulator_tests do |options|
    environment = options[:environment] || "development"

    UI.header "📱 Running iOS simulator tests"

    begin
      # Build for testing
      build_for_testing(environment: environment)

      # Run Flutter integration tests
      sh("cd .. && flutter test integration_test/apple_signin_test.dart")

      UI.success "✅ iOS simulator tests passed"
    rescue => ex
      UI.error "❌ iOS simulator tests failed: #{ex.message}"
      raise ex
    end
  end

  # ============================================================================
  # UTILITY LANES
  # ============================================================================

  desc "Clean and reset Apple Sign-In configuration"
  lane :clean_apple_signin_config do
    UI.header "🧹 Cleaning Apple Sign-In configuration"

    # Clean derived data
    clear_derived_data

    # Clean pods
    sh("cd ../ios && pod clean && pod install")

    # Reset provisioning profiles
    sh("rm -rf ~/Library/MobileDevice/Provisioning\\ Profiles/*")

    UI.success "✅ Apple Sign-In configuration cleaned"
  end

# ============================================================================
# CROSS-PLATFORM LANES
# ============================================================================

platform :android do

  desc "Configure Apple Sign-In for Android platform"
  lane :setup_android_apple_signin do
    UI.header "🤖 Setting up Apple Sign-In for Android"

    # Android uses web authentication flow for Apple Sign-In
    # No special configuration needed beyond Firebase setup

    # Validate Firebase configuration
    validate_android_firebase_config

    # Update Android manifest if needed
    update_android_manifest_for_apple_signin

    UI.success "✅ Android Apple Sign-In setup completed"
  end

  desc "Validate Android Firebase configuration"
  lane :validate_android_firebase_config do
    UI.message "🔍 Validating Android Firebase configuration"

    firebase_config_path = "android/app/google-services.json"

    unless File.exist?(firebase_config_path)
      UI.error "❌ Firebase configuration file not found: #{firebase_config_path}"
      UI.message "Please download google-services.json from Firebase Console"
      return false
    end

    begin
      firebase_config = JSON.parse(File.read(firebase_config_path))

      # Validate required fields
      project_info = firebase_config["project_info"]
      unless project_info && project_info["project_id"] == FIREBASE_PROJECT_ID
        UI.error "❌ Firebase project ID mismatch"
        return false
      end

      UI.success "✅ Android Firebase configuration valid"
      return true

    rescue JSON::ParserError => ex
      UI.error "❌ Invalid Firebase configuration JSON: #{ex.message}"
      return false
    end
  end

  desc "Update Android manifest for Apple Sign-In"
  lane :update_android_manifest_for_apple_signin do
    UI.message "📝 Updating Android manifest for Apple Sign-In"

    manifest_path = "android/app/src/main/AndroidManifest.xml"

    unless File.exist?(manifest_path)
      UI.error "❌ Android manifest not found: #{manifest_path}"
      return false
    end

    # Android doesn't require special manifest changes for Apple Sign-In
    # as it uses web authentication flow
    UI.success "✅ Android manifest is compatible with Apple Sign-In"
    return true
  end

end

# ============================================================================
# WEB PLATFORM LANES
# ============================================================================

desc "Configure Apple Sign-In for Web platform"
lane :setup_web_apple_signin do
  UI.header "🌐 Setting up Apple Sign-In for Web platform"

  # Configure Apple Sign-In service ID
  configure_apple_signin_service_id

  # Update web configuration files
  update_web_configuration

  # Validate web configuration
  validate_web_apple_signin_config

  UI.success "✅ Web Apple Sign-In setup completed"
end

desc "Validate web Apple Sign-In configuration"
lane :validate_web_apple_signin_config do
  UI.message "🔍 Validating web Apple Sign-In configuration"

  errors = []

  # Check web/index.html
  web_index_path = "web/index.html"
  if File.exist?(web_index_path)
    content = File.read(web_index_path)

    # Check for Apple Sign-In meta tag
    unless content.include?('name="appleid-signin-client-id"')
      errors << "Apple Sign-In client ID meta tag missing in web/index.html"
    end

    # Check for correct service ID
    unless content.include?(APPLE_SIGNIN_SERVICE_ID)
      errors << "Incorrect Apple Sign-In service ID in web/index.html"
    end
  else
    errors << "web/index.html not found"
  end

  # Check Firebase configuration for web
  firebase_config_path = "web/firebase-config.js"
  if File.exist?(firebase_config_path)
    content = File.read(firebase_config_path)
    unless content.include?(FIREBASE_PROJECT_ID)
      errors << "Firebase project ID mismatch in web configuration"
    end
  end

  if errors.empty?
    UI.success "✅ Web Apple Sign-In configuration valid"
    return true
  else
    errors.each { |error| UI.error "❌ #{error}" }
    return false
  end
end

# ============================================================================
# MULTI-PLATFORM SETUP LANES
# ============================================================================

desc "Setup Apple Sign-In for all platforms and environments"
lane :setup_apple_signin_complete do
  UI.header "🚀 Complete Apple Sign-In Setup for All Platforms"

  # iOS setup for all environments
  ["production", "staging", "development"].each do |env|
    UI.message "Setting up iOS for #{env} environment"
    setup_apple_signin(environment: env)
  end

  # Android setup
  UI.message "Setting up Android platform"
  setup_android_apple_signin

  # Web setup
  UI.message "Setting up Web platform"
  setup_web_apple_signin

  # Final validation
  validate_complete_apple_signin_setup

  UI.success "✅ Complete Apple Sign-In setup finished!"
  UI.message "🎉 Apple Sign-In is now configured for all platforms and environments"
end

desc "Validate complete Apple Sign-In setup"
lane :validate_complete_apple_signin_setup do
  UI.header "🔍 Validating Complete Apple Sign-In Setup"

  validation_results = {}

  # Validate iOS environments
  ["production", "staging", "development"].each do |env|
    UI.message "Validating iOS #{env} environment"
    validation_results["ios_#{env}"] = validate_apple_signin_setup(environment: env)
  end

  # Validate Android
  UI.message "Validating Android platform"
  validation_results["android"] = validate_android_firebase_config

  # Validate Web
  UI.message "Validating Web platform"
  validation_results["web"] = validate_web_apple_signin_config

  # Summary
  UI.header "📊 Validation Summary"
  validation_results.each do |platform, result|
    status = result ? "✅ PASS" : "❌ FAIL"
    UI.message "#{platform.upcase}: #{status}"
  end

  all_passed = validation_results.values.all?
  if all_passed
    UI.success "🎉 All platforms validated successfully!"
  else
    UI.error "❌ Some platforms failed validation. Please check the logs above."
  end

  return all_passed
end

# ============================================================================
# DEPLOYMENT AND CI/CD INTEGRATION LANES
# ============================================================================

desc "Prepare Apple Sign-In configuration for CI/CD deployment"
lane :prepare_for_deployment do |options|
  environment = options[:environment] || "production"

  UI.header "🚀 Preparing Apple Sign-In for #{environment} deployment"

  # Clean previous builds
  clean_apple_signin_config

  # Setup for specific environment
  setup_apple_signin(environment: environment)

  # Build and validate
  build_for_testing(environment: environment)

  # Run tests
  test_apple_signin(environment: environment)

  UI.success "✅ Ready for #{environment} deployment"
end

desc "Post-deployment validation"
lane :post_deployment_validation do |options|
  environment = options[:environment] || "production"

  UI.header "🔍 Post-deployment validation for #{environment}"

  # Validate configuration
  validate_apple_signin_setup(environment: environment)

  # Run integration tests
  run_web_authentication_tests

  UI.success "✅ Post-deployment validation completed"
end

end
  
  desc "Show Apple Sign-In configuration status"
  lane :show_apple_signin_status do
    UI.header "📊 Apple Sign-In Configuration Status"
    
    ["production", "staging", "development"].each do |env|
      bundle_id = get_bundle_id_for_environment(env)
      UI.message "#{env.capitalize}: #{bundle_id}"
      
      # Check if entitlements file exists and has Apple Sign-In capability
      entitlements_path = get_entitlements_path(env)
      if File.exist?(entitlements_path)
        UI.success "  ✅ Entitlements file exists"
        # Check for Apple Sign-In capability in entitlements
        if File.read(entitlements_path).include?("com.apple.developer.applesignin")
          UI.success "  ✅ Apple Sign-In capability enabled"
        else
          UI.error "  ❌ Apple Sign-In capability missing"
        end
      else
        UI.error "  ❌ Entitlements file missing"
      end
    end
    
    # Check Firebase configuration
    firebase_config_path = "ios/Runner/GoogleService-Info.plist"
    if File.exist?(firebase_config_path)
      UI.success "✅ Firebase configuration exists"
    else
      UI.error "❌ Firebase configuration missing"
    end
    
    # Check Apple Sign-In service ID configuration
    UI.message "Apple Sign-In Service ID: #{APPLE_SIGNIN_SERVICE_ID}"
  end
  
  # ============================================================================
  # HELPER METHODS
  # ============================================================================
  
  private_lane :setup_app_store_connect_api do
    app_store_connect_api_key(
      key_id: ENV["APP_STORE_CONNECT_API_KEY_ID"],
      issuer_id: ENV["APP_STORE_CONNECT_API_ISSUER_ID"],
      key_content: ENV["APP_STORE_CONNECT_API_KEY_CONTENT"],
      duration: 1200,
      in_house: false
    )
  end
  
  private_lane :validate_environment do |environment|
    valid_environments = ["production", "staging", "development"]
    unless valid_environments.include?(environment)
      UI.user_error!("Invalid environment: #{environment}. Valid options: #{valid_environments.join(', ')}")
    end
  end
  
  private_lane :get_bundle_id_for_environment do |environment|
    case environment
    when "production"
      "com.algomash.bloomg.bloomg-flutter"
    when "staging"
      "com.algomash.bloomg.bloomg-flutter.stg"
    when "development"
      "com.algomash.bloomg.bloomg-flutter.dev"
    else
      UI.user_error!("Unknown environment: #{environment}")
    end
  end
  
  private_lane :get_build_configuration do |environment|
    case environment
    when "production"
      "Release-Production"
    when "staging"
      "Release-Staging"
    when "development"
      "Debug-Development"
    else
      "Release"
    end
  end
  
  private_lane :get_entitlements_path do |environment|
    case environment
    when "development"
      "ios/Runner/RunnerDebug.entitlements"
    else
      "ios/Runner/Runner.entitlements"
    end
  end
  
end
