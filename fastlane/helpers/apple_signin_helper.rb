# Apple Sign-In Helper Methods for Fastlane
# This file contains utility methods for Apple Sign-In configuration

require 'plist'
require 'xcodeproj'

module AppleSigninHelper
  
  # ============================================================================
  # ENTITLEMENTS MANAGEMENT
  # ============================================================================
  
  def self.ensure_apple_signin_entitlement(environment:)
    entitlements_path = get_entitlements_path(environment)
    
    UI.message "📝 Ensuring Apple Sign-In entitlement in #{entitlements_path}"
    
    # Create entitlements file if it doesn't exist
    unless File.exist?(entitlements_path)
      create_entitlements_file(entitlements_path)
    end
    
    # Read existing entitlements
    entitlements = Plist.parse_xml(entitlements_path) || {}
    
    # Add Apple Sign-In capability if not present
    apple_signin_key = "com.apple.developer.applesignin"
    unless entitlements.key?(apple_signin_key)
      entitlements[apple_signin_key] = ["Default"]
      
      # Write updated entitlements
      File.write(entitlements_path, entitlements.to_plist)
      UI.success "✅ Added Apple Sign-In entitlement to #{entitlements_path}"
    else
      UI.message "ℹ️  Apple Sign-In entitlement already exists"
    end
  end
  
  def self.create_entitlements_file(path)
    UI.message "📄 Creating entitlements file at #{path}"
    
    entitlements = {
      "com.apple.developer.applesignin" => ["Default"]
    }
    
    # Ensure directory exists
    FileUtils.mkdir_p(File.dirname(path))
    
    # Write entitlements file
    File.write(path, entitlements.to_plist)
    UI.success "✅ Created entitlements file"
  end
  
  # ============================================================================
  # XCODE PROJECT MANAGEMENT
  # ============================================================================
  
  def self.enable_apple_signin_capability(bundle_id:)
    UI.message "🔧 Enabling Apple Sign-In capability for #{bundle_id}"
    
    begin
      # Use spaceship to enable capability
      Spaceship::Portal.login(ENV["APPLE_ID"])
      Spaceship::Portal.select_team(team_id: ENV["APPLE_TEAM_ID"])
      
      # Find the app
      app = Spaceship::Portal::App.find(bundle_id)
      
      if app.nil?
        UI.error "❌ App with bundle ID #{bundle_id} not found"
        return false
      end
      
      # Enable Apple Sign-In capability
      app.update_service(Spaceship::Portal::AppService.sign_in_with_apple.on)
      
      UI.success "✅ Apple Sign-In capability enabled for #{bundle_id}"
      return true
      
    rescue => ex
      UI.error "❌ Failed to enable Apple Sign-In capability: #{ex.message}"
      return false
    end
  end
  
  def self.update_xcode_project_capabilities(environment:)
    project_path = "ios/Runner.xcodeproj"
    
    UI.message "🔨 Updating Xcode project capabilities"
    
    begin
      project = Xcodeproj::Project.open(project_path)
      target = project.targets.find { |t| t.name == "Runner" }
      
      if target.nil?
        UI.error "❌ Runner target not found in Xcode project"
        return false
      end
      
      # Add Apple Sign-In capability to project settings
      build_configuration = get_build_configuration_name(environment)
      config = target.build_configurations.find { |c| c.name == build_configuration }
      
      if config
        # Ensure code signing entitlements is set
        entitlements_path = get_entitlements_path(environment)
        relative_entitlements_path = entitlements_path.gsub("ios/", "")
        config.build_settings["CODE_SIGN_ENTITLEMENTS"] = relative_entitlements_path
        
        project.save
        UI.success "✅ Updated Xcode project capabilities"
        return true
      else
        UI.error "❌ Build configuration #{build_configuration} not found"
        return false
      end
      
    rescue => ex
      UI.error "❌ Failed to update Xcode project: #{ex.message}"
      return false
    end
  end
  
  # ============================================================================
  # PROVISIONING PROFILE MANAGEMENT
  # ============================================================================
  
  def self.update_provisioning_profiles(bundle_id:, environment:)
    UI.message "📋 Updating provisioning profiles for #{bundle_id}"
    
    begin
      # Generate and download provisioning profiles
      sigh(
        app_identifier: bundle_id,
        force: true,
        filename: "#{environment}_provisioning_profile.mobileprovision"
      )
      
      UI.success "✅ Provisioning profile updated"
      return true
      
    rescue => ex
      UI.error "❌ Failed to update provisioning profile: #{ex.message}"
      return false
    end
  end
  
  # ============================================================================
  # VALIDATION METHODS
  # ============================================================================
  
  def self.validate_ios_configuration(environment:)
    UI.message "🔍 Validating iOS configuration for #{environment}"
    
    errors = []
    
    # Check entitlements file
    entitlements_path = get_entitlements_path(environment)
    unless File.exist?(entitlements_path)
      errors << "Entitlements file missing: #{entitlements_path}"
    else
      entitlements = Plist.parse_xml(entitlements_path)
      unless entitlements&.key?("com.apple.developer.applesignin")
        errors << "Apple Sign-In entitlement missing in #{entitlements_path}"
      end
    end
    
    # Check Xcode project configuration
    project_path = "ios/Runner.xcodeproj"
    unless File.exist?(project_path)
      errors << "Xcode project not found: #{project_path}"
    end
    
    # Check Firebase configuration
    firebase_config_path = "ios/Runner/GoogleService-Info.plist"
    unless File.exist?(firebase_config_path)
      errors << "Firebase configuration missing: #{firebase_config_path}"
    end
    
    if errors.empty?
      UI.success "✅ iOS configuration validation passed"
      return true
    else
      errors.each { |error| UI.error "❌ #{error}" }
      return false
    end
  end
  
  def self.validate_firebase_configuration
    UI.message "🔍 Validating Firebase configuration"
    
    firebase_config_path = "ios/Runner/GoogleService-Info.plist"
    
    unless File.exist?(firebase_config_path)
      UI.error "❌ Firebase configuration file not found"
      return false
    end
    
    begin
      firebase_config = Plist.parse_xml(firebase_config_path)
      
      required_keys = ["PROJECT_ID", "BUNDLE_ID", "API_KEY"]
      missing_keys = required_keys.select { |key| !firebase_config.key?(key) }
      
      if missing_keys.empty?
        UI.success "✅ Firebase configuration validation passed"
        return true
      else
        UI.error "❌ Missing Firebase configuration keys: #{missing_keys.join(', ')}"
        return false
      end
      
    rescue => ex
      UI.error "❌ Failed to parse Firebase configuration: #{ex.message}"
      return false
    end
  end
  
  def self.validate_developer_portal_configuration(environment:)
    UI.message "🔍 Validating Apple Developer Portal configuration"
    
    bundle_id = get_bundle_id_for_environment(environment)
    
    begin
      Spaceship::Portal.login(ENV["APPLE_ID"])
      Spaceship::Portal.select_team(team_id: ENV["APPLE_TEAM_ID"])
      
      app = Spaceship::Portal::App.find(bundle_id)
      
      if app.nil?
        UI.error "❌ App with bundle ID #{bundle_id} not found in Developer Portal"
        return false
      end
      
      # Check if Apple Sign-In capability is enabled
      services = app.details.features
      apple_signin_enabled = services["APG3427HIY"]&.enabled # Apple Sign-In service ID
      
      if apple_signin_enabled
        UI.success "✅ Apple Sign-In capability enabled in Developer Portal"
        return true
      else
        UI.error "❌ Apple Sign-In capability not enabled in Developer Portal"
        return false
      end
      
    rescue => ex
      UI.error "❌ Failed to validate Developer Portal configuration: #{ex.message}"
      return false
    end
  end
  
  # ============================================================================
  # UTILITY METHODS
  # ============================================================================
  
  def self.get_entitlements_path(environment)
    case environment
    when "development"
      "ios/Runner/RunnerDebug.entitlements"
    else
      "ios/Runner/Runner.entitlements"
    end
  end
  
  def self.get_bundle_id_for_environment(environment)
    case environment
    when "production"
      "com.algomash.bloomg.bloomg-flutter"
    when "staging"
      "com.algomash.bloomg.bloomg-flutter.stg"
    when "development"
      "com.algomash.bloomg.bloomg-flutter.dev"
    else
      UI.user_error!("Unknown environment: #{environment}")
    end
  end
  
  def self.get_build_configuration_name(environment)
    case environment
    when "production"
      "Release-Production"
    when "staging"
      "Release-Staging"
    when "development"
      "Debug-Development"
    else
      "Release"
    end
  end
  
  # ============================================================================
  # WEB PLATFORM CONFIGURATION
  # ============================================================================
  
  def self.configure_apple_signin_service_id
    UI.message "🌐 Configuring Apple Sign-In Service ID for web platform"
    
    service_id = "com.algomash.radiance"
    
    begin
      Spaceship::Portal.login(ENV["APPLE_ID"])
      Spaceship::Portal.select_team(team_id: ENV["APPLE_TEAM_ID"])
      
      # Find or create the service ID
      service = Spaceship::Portal::WebsiteApp.find(service_id)
      
      if service.nil?
        UI.message "Creating new Service ID: #{service_id}"
        service = Spaceship::Portal::WebsiteApp.create!(
          bundle_id: service_id,
          name: "Sign in with Apple - Bloomg"
        )
      end
      
      # Configure the service for Sign in with Apple
      service.update_service(Spaceship::Portal::AppService.sign_in_with_apple.on)
      
      UI.success "✅ Apple Sign-In Service ID configured"
      return true
      
    rescue => ex
      UI.error "❌ Failed to configure Service ID: #{ex.message}"
      return false
    end
  end
  
  def self.update_web_configuration
    UI.message "🌐 Updating web configuration files"
    
    # Update web/index.html with Apple Sign-In meta tags if needed
    web_index_path = "web/index.html"
    
    if File.exist?(web_index_path)
      content = File.read(web_index_path)
      
      # Add Apple Sign-In meta tag if not present
      apple_signin_meta = '<meta name="appleid-signin-client-id" content="com.algomash.radiance">'
      
      unless content.include?(apple_signin_meta)
        # Insert before closing head tag
        updated_content = content.gsub("</head>", "  #{apple_signin_meta}\n</head>")
        File.write(web_index_path, updated_content)
        UI.success "✅ Updated web/index.html with Apple Sign-In configuration"
      else
        UI.message "ℹ️  Web configuration already up to date"
      end
    else
      UI.warning "⚠️  web/index.html not found"
    end
  end
  
  def self.validate_android_configuration
    UI.message "🤖 Validating Android configuration"
    
    # Check if Android manifest has necessary configurations
    manifest_path = "android/app/src/main/AndroidManifest.xml"
    
    unless File.exist?(manifest_path)
      UI.error "❌ Android manifest not found"
      return false
    end
    
    UI.success "✅ Android configuration validation passed"
    return true
  end
  
  def self.update_android_configuration
    UI.message "🤖 Updating Android configuration"
    
    # Android doesn't require special configuration for Apple Sign-In
    # as it uses web authentication flow
    UI.success "✅ Android configuration updated (web flow)"
  end
  
end

# Make helper methods available in Fastfile
def ensure_apple_signin_entitlement(environment:)
  AppleSigninHelper.ensure_apple_signin_entitlement(environment: environment)
end

def enable_apple_signin_capability(bundle_id:)
  AppleSigninHelper.enable_apple_signin_capability(bundle_id: bundle_id)
end

def update_provisioning_profiles(bundle_id:, environment:)
  AppleSigninHelper.update_provisioning_profiles(bundle_id: bundle_id, environment: environment)
end

def validate_ios_configuration(environment:)
  AppleSigninHelper.validate_ios_configuration(environment: environment)
end

def validate_firebase_configuration
  AppleSigninHelper.validate_firebase_configuration
end

def validate_developer_portal_configuration(environment:)
  AppleSigninHelper.validate_developer_portal_configuration(environment: environment)
end

def configure_apple_signin_service_id
  AppleSigninHelper.configure_apple_signin_service_id
end

def update_web_configuration
  AppleSigninHelper.update_web_configuration
end

def validate_android_configuration
  AppleSigninHelper.validate_android_configuration
end

def update_android_configuration
  AppleSigninHelper.update_android_configuration
end
