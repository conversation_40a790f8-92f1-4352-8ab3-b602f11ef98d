# Apple Sign-In Web Authentication Troubleshooting Guide

## Overview

This guide helps resolve the `UNKNOWN_SIWA_ERROR` that occurs during Apple Sign-In web authentication. This error typically indicates configuration issues between Apple Developer Portal, Firebase, and your web application.

## Quick Diagnosis

Run the debugging script to identify issues:

```bash
./scripts/debug_apple_signin_web.sh
```

## Common Causes and Solutions

### 1. **Firebase Configuration Mismatch** ✅ FIXED

**Issue**: Inconsistent Firebase configuration between `web/firebase-config.js` and `lib/firebase_options.dart`

**Solution**: Updated `web/firebase-config.js` with correct values:
- ✅ App ID: `1:1068175978703:web:a62aa1bf9668a7b9dcc7ba`
- ✅ Storage Bucket: `bloomg-flutter.firebasestorage.app`
- ✅ Measurement ID: `G-17QGQMB6GZ`

### 2. **Missing Apple Sign-In JavaScript SDK** ✅ FIXED

**Issue**: Apple Sign-In JavaScript SDK not loaded in web application

**Solution**: Added SDK to `web/index.html`:
```html
<script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
```

### 3. **Apple Developer Portal Service ID Configuration** ⚠️ REQUIRES VERIFICATION

**Issue**: Service ID `com.algomash.radiance` may not be properly configured for web authentication

**Required Actions**:

1. **Verify Service ID Configuration**:
   - Go to [Apple Developer Portal](https://developer.apple.com/account/resources/identifiers/list/serviceId)
   - Find Service ID: `com.algomash.radiance`
   - Ensure "Sign In with Apple" capability is enabled
   - Verify configuration matches your setup

2. **Domain Verification**:
   - Add domain: `bloomg-flutter.firebaseapp.com`
   - Verify domain ownership (Apple will provide verification file)
   - Ensure domain verification status is "Verified"

3. **Redirect URI Configuration**:
   - Add redirect URI: `https://bloomg-flutter.firebaseapp.com/__/auth/handler`
   - Ensure it matches exactly (case-sensitive)

### 4. **Firebase Console Apple Provider Configuration** ⚠️ REQUIRES VERIFICATION

**Required Actions**:

1. **Enable Apple Sign-In Provider**:
   - Go to [Firebase Console](https://console.firebase.google.com/project/bloomg-flutter/authentication/providers)
   - Navigate to Authentication > Sign-in method
   - Ensure Apple provider is enabled

2. **Verify Configuration**:
   - Service ID: `com.algomash.radiance`
   - Team ID: `598AQBZ36R`
   - Key ID: `99PLQH6WMP`
   - Private Key: Ensure valid .p8 file is uploaded

## Testing Steps

### 1. Local Development Testing

```bash
# Start Flutter web development server
flutter run -d chrome --web-port 3000

# Test Apple Sign-In at http://localhost:3000
```

### 2. Browser Console Debugging

1. Open Developer Tools (F12)
2. Go to Console tab
3. Attempt Apple Sign-In
4. Look for specific error messages:
   - `invalid_client`: Service ID configuration issue
   - `invalid_request`: Missing or incorrect parameters
   - `unauthorized_client`: Domain not verified

### 3. Network Request Analysis

Monitor network requests during Apple Sign-In:
1. Open Developer Tools > Network tab
2. Filter by "appleid.apple.com"
3. Attempt sign-in
4. Check request/response details for errors

## Advanced Debugging

### Check Apple Sign-In Service Status

```javascript
// Run in browser console to check Apple ID SDK
console.log('Apple ID SDK loaded:', typeof window.AppleID !== 'undefined');

// Check configuration
console.log('Client ID:', document.querySelector('meta[name="appleid-signin-client-id"]')?.content);
console.log('Redirect URI:', document.querySelector('meta[name="appleid-signin-redirect-uri"]')?.content);
```

### Verify Firebase Configuration

```javascript
// Check Firebase config in browser console
console.log('Firebase Config:', window.firebaseConfig);
```

## Production Deployment Checklist

- [ ] Apple Developer Portal Service ID verified
- [ ] Domain `bloomg-flutter.firebaseapp.com` verified
- [ ] Firebase Apple provider enabled and configured
- [ ] Web app deployed to Firebase Hosting
- [ ] HTTPS enabled (required for Apple Sign-In)
- [ ] Apple Sign-In JavaScript SDK loading correctly
- [ ] Meta tags present in production HTML

## Error Code Reference

| Error | Cause | Solution |
|-------|-------|----------|
| `UNKNOWN_SIWA_ERROR` | General configuration issue | Follow this troubleshooting guide |
| `invalid_client` | Service ID not found/configured | Verify Apple Developer Portal setup |
| `invalid_request` | Missing parameters | Check meta tags and implementation |
| `unauthorized_client` | Domain not verified | Complete domain verification process |

## Support Resources

- [Apple Sign-In Documentation](https://developer.apple.com/documentation/sign_in_with_apple)
- [Firebase Apple Sign-In Guide](https://firebase.google.com/docs/auth/web/apple)
- [Apple Developer Portal](https://developer.apple.com/account/)

## Next Steps

1. Run the debugging script: `./scripts/debug_apple_signin_web.sh`
2. Address any issues identified by the script
3. Verify Apple Developer Portal configuration
4. Test the authentication flow
5. Monitor browser console for detailed error messages

If issues persist after following this guide, the problem is likely in the Apple Developer Portal domain verification or Service ID configuration.
