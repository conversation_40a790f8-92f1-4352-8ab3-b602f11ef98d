#!/bin/bash

# Apple Sign-In Web Authentication Debugging Script
# This script helps diagnose and resolve UNKNOWN_SIWA_ERROR issues

set -e

echo "🔍 Apple Sign-In Web Authentication Debugging Script"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if we're in the correct directory
if [ ! -f "pubspec.yaml" ]; then
    print_status "ERROR" "Please run this script from the Flutter project root directory"
    exit 1
fi

echo ""
echo "1. 🔍 Checking Firebase Configuration"
echo "======================================"

# Check Firebase options file
if [ -f "lib/firebase_options.dart" ]; then
    print_status "SUCCESS" "Firebase options file exists"
    
    # Extract web app ID
    WEB_APP_ID=$(grep -o "appId: '[^']*'" lib/firebase_options.dart | grep "web:" | cut -d"'" -f2)
    if [ -n "$WEB_APP_ID" ]; then
        print_status "SUCCESS" "Web App ID found: $WEB_APP_ID"
    else
        print_status "ERROR" "Web App ID not found in firebase_options.dart"
    fi
    
    # Check auth domain
    AUTH_DOMAIN=$(grep -o "authDomain: '[^']*'" lib/firebase_options.dart | cut -d"'" -f2)
    if [ "$AUTH_DOMAIN" = "bloomg-flutter.firebaseapp.com" ]; then
        print_status "SUCCESS" "Auth domain is correct: $AUTH_DOMAIN"
    else
        print_status "ERROR" "Auth domain mismatch: $AUTH_DOMAIN"
    fi
else
    print_status "ERROR" "Firebase options file not found"
fi

# Check web Firebase config
if [ -f "web/firebase-config.js" ]; then
    print_status "SUCCESS" "Web Firebase config file exists"
    
    # Check if app ID matches
    WEB_CONFIG_APP_ID=$(grep -o '"appId": "[^"]*"' web/firebase-config.js | cut -d'"' -f4)
    if [ "$WEB_CONFIG_APP_ID" = "$WEB_APP_ID" ]; then
        print_status "SUCCESS" "Web config app ID matches Firebase options"
    else
        print_status "ERROR" "Web config app ID mismatch: $WEB_CONFIG_APP_ID vs $WEB_APP_ID"
    fi
else
    print_status "WARNING" "Web Firebase config file not found"
fi

echo ""
echo "2. 🍎 Checking Apple Sign-In Configuration"
echo "=========================================="

# Check web/index.html for Apple Sign-In meta tags
if [ -f "web/index.html" ]; then
    print_status "SUCCESS" "Web index.html exists"
    
    # Check for Apple Sign-In client ID
    if grep -q 'name="appleid-signin-client-id"' web/index.html; then
        CLIENT_ID=$(grep -o 'content="[^"]*"' web/index.html | grep -A1 'appleid-signin-client-id' | tail -1 | cut -d'"' -f2)
        print_status "SUCCESS" "Apple Sign-In client ID found: $CLIENT_ID"
    else
        print_status "ERROR" "Apple Sign-In client ID meta tag missing"
    fi
    
    # Check for redirect URI
    if grep -q 'name="appleid-signin-redirect-uri"' web/index.html; then
        REDIRECT_URI=$(grep -o 'content="[^"]*"' web/index.html | grep -A1 'appleid-signin-redirect-uri' | tail -1 | cut -d'"' -f2)
        print_status "SUCCESS" "Redirect URI found: $REDIRECT_URI"
    else
        print_status "ERROR" "Apple Sign-In redirect URI meta tag missing"
    fi
    
    # Check for Apple Sign-In JavaScript SDK
    if grep -q 'appleid.cdn-apple.com' web/index.html; then
        print_status "SUCCESS" "Apple Sign-In JavaScript SDK included"
    else
        print_status "WARNING" "Apple Sign-In JavaScript SDK not found"
    fi
else
    print_status "ERROR" "Web index.html not found"
fi

echo ""
echo "3. 📱 Checking Flutter Implementation"
echo "===================================="

# Check Firebase Auth repository
if [ -f "lib/auth/repository/firebase_auth_repository.dart" ]; then
    print_status "SUCCESS" "Firebase auth repository exists"
    
    # Check for webAuthenticationOptions
    if grep -q "webAuthenticationOptions" lib/auth/repository/firebase_auth_repository.dart; then
        print_status "SUCCESS" "webAuthenticationOptions configured"
        
        # Check client ID in code
        CODE_CLIENT_ID=$(grep -A5 "webAuthenticationOptions" lib/auth/repository/firebase_auth_repository.dart | grep -o "clientId: '[^']*'" | cut -d"'" -f2)
        if [ "$CODE_CLIENT_ID" = "$CLIENT_ID" ]; then
            print_status "SUCCESS" "Client ID matches between HTML and Dart code"
        else
            print_status "ERROR" "Client ID mismatch: HTML=$CLIENT_ID, Dart=$CODE_CLIENT_ID"
        fi
    else
        print_status "ERROR" "webAuthenticationOptions not found in implementation"
    fi
else
    print_status "ERROR" "Firebase auth repository not found"
fi

echo ""
echo "4. 🌐 Network and Domain Verification"
echo "====================================="

# Test Firebase auth domain accessibility
print_status "INFO" "Testing Firebase auth domain accessibility..."
if curl -s --head "https://bloomg-flutter.firebaseapp.com" | head -n 1 | grep -q "200 OK"; then
    print_status "SUCCESS" "Firebase auth domain is accessible"
else
    print_status "WARNING" "Firebase auth domain may not be accessible"
fi

# Test Apple Sign-In JavaScript SDK
print_status "INFO" "Testing Apple Sign-In JavaScript SDK..."
if curl -s --head "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js" | head -n 1 | grep -q "200 OK"; then
    print_status "SUCCESS" "Apple Sign-In JavaScript SDK is accessible"
else
    print_status "ERROR" "Apple Sign-In JavaScript SDK is not accessible"
fi

echo ""
echo "5. 🔧 Recommended Actions"
echo "========================"

print_status "INFO" "Based on the analysis above, here are the recommended actions:"

echo ""
echo "🔧 IMMEDIATE FIXES:"
echo "1. Verify Apple Developer Portal Service ID configuration:"
echo "   - Go to https://developer.apple.com/account/resources/identifiers/list/serviceId"
echo "   - Find Service ID: com.algomash.radiance"
echo "   - Ensure 'Sign In with Apple' is enabled"
echo "   - Verify domain: bloomg-flutter.firebaseapp.com is added and verified"
echo "   - Confirm redirect URI: https://bloomg-flutter.firebaseapp.com/__/auth/handler"

echo ""
echo "2. Test the web authentication flow:"
echo "   flutter run -d chrome --web-port 3000"
echo "   # Then test Apple Sign-In on http://localhost:3000"

echo ""
echo "3. Check browser console for detailed errors:"
echo "   - Open Developer Tools (F12)"
echo "   - Go to Console tab"
echo "   - Attempt Apple Sign-In"
echo "   - Look for specific error messages"

echo ""
echo "4. Verify Firebase Console Apple Sign-In configuration:"
echo "   - Go to Firebase Console > Authentication > Sign-in method"
echo "   - Ensure Apple provider is enabled"
echo "   - Verify Service ID and Team ID are correct"

echo ""
print_status "SUCCESS" "Debugging script completed!"
print_status "INFO" "If issues persist, check the Apple Developer Portal domain verification status"
