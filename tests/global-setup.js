// Global Setup for Playwright Tests
// This file contains setup logic that runs before all tests

const { chromium } = require('@playwright/test');

async function globalSetup(config) {
  console.log('🚀 Starting global setup for Apple Sign-In tests...');
  
  // Environment validation
  validateEnvironment();
  
  // Setup test data
  await setupTestData();
  
  // Validate Flutter web server
  await validateFlutterWebServer(config);
  
  console.log('✅ Global setup completed successfully');
}

function validateEnvironment() {
  console.log('🔍 Validating test environment...');
  
  const requiredEnvVars = [
    'FLUTTER_WEB_URL',
    'TEST_ENVIRONMENT'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn(`⚠️  Missing environment variables: ${missingVars.join(', ')}`);
    console.log('Using default values for missing variables');
  }
  
  // Set defaults
  process.env.FLUTTER_WEB_URL = process.env.FLUTTER_WEB_URL || 'http://localhost:3000';
  process.env.TEST_ENVIRONMENT = process.env.TEST_ENVIRONMENT || 'development';
  
  console.log(`📍 Test URL: ${process.env.FLUTTER_WEB_URL}`);
  console.log(`🏷️  Environment: ${process.env.TEST_ENVIRONMENT}`);
}

async function setupTestData() {
  console.log('📋 Setting up test data...');
  
  // Create test data directory if it doesn't exist
  const fs = require('fs');
  const path = require('path');
  
  const testDataDir = path.join(__dirname, 'test-data');
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }
  
  // Create test configuration file
  const testConfig = {
    appleSignIn: {
      serviceId: 'com.algomash.radiance',
      redirectUri: 'https://bloomg-flutter.firebaseapp.com/__/auth/handler',
      scopes: ['name', 'email']
    },
    firebase: {
      projectId: 'bloomg-flutter',
      apiKey: 'AIzaSyA3mIlC6O1_tGeH7OpuZmEcDYSDPcNcMqo'
    },
    testUsers: {
      validAppleUser: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    }
  };
  
  const configPath = path.join(testDataDir, 'test-config.json');
  fs.writeFileSync(configPath, JSON.stringify(testConfig, null, 2));
  
  console.log('✅ Test data setup completed');
}

async function validateFlutterWebServer(config) {
  console.log('🌐 Validating Flutter web server...');
  
  const baseURL = config.use?.baseURL || process.env.FLUTTER_WEB_URL;
  
  if (!baseURL) {
    console.warn('⚠️  No base URL configured, skipping server validation');
    return;
  }
  
  try {
    // Launch a browser to check if the server is running
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // Set a shorter timeout for this check
    page.setDefaultTimeout(10000);
    
    await page.goto(baseURL);
    
    // Check if Flutter app loads
    await page.waitForSelector('flutter-view', { timeout: 5000 });
    
    await browser.close();
    
    console.log('✅ Flutter web server is running and accessible');
  } catch (error) {
    console.warn(`⚠️  Flutter web server validation failed: ${error.message}`);
    console.log('Tests may fail if the server is not running properly');
  }
}

module.exports = globalSetup;
