# Bitbucket Pipelines configuration for Flutter project
# This configuration provides CI/CD for the Bloomg Flutter application

image: cirrusci/flutter:stable

definitions:
  caches:
    flutter: ~/.pub-cache
    bundler: vendor/bundle
  steps:
    - step: &setup-apple-signin
        name: Setup Apple Sign-In Configuration
        caches:
          - bundler
        script:
          # Install Ruby dependencies
          - cd fastlane && bundle install --path vendor/bundle
          # Setup Apple Sign-In for all platforms
          - bundle exec fastlane setup_apple_signin_complete
          # Validate configuration
          - bundle exec fastlane validate_complete_apple_signin_setup
        artifacts:
          - fastlane/certificates/**
          - fastlane/profiles/**
    - step: &flutter-test
        name: Flutter Test
        caches:
          - flutter
        script:
          - flutter --version
          - flutter pub get
          - flutter analyze
          - flutter test --coverage --test-randomize-ordering-seed random
        artifacts:
          - coverage/**
    - step: &flutter-build-android
        name: Build Android APK
        caches:
          - flutter
        script:
          - flutter --version
          - flutter pub get
          - flutter build apk --flavor production --target lib/main_production.dart
        artifacts:
          - build/app/outputs/flutter-apk/*.apk
    - step: &flutter-build-ios
        name: Build iOS with Apple Sign-In
        caches:
          - flutter
          - bundler
        script:
          - flutter --version
          - flutter pub get
          # Setup Apple Sign-In configuration for iOS
          - cd fastlane && bundle install --path vendor/bundle
          - bundle exec fastlane ios setup_apple_signin environment:production
          # Build iOS app
          - flutter build ios --flavor production --target lib/main_production.dart --no-codesign
        artifacts:
          - build/ios/iphoneos/*.app
          - fastlane/certificates/**
          - fastlane/profiles/**

pipelines:
  default:
    - step: *flutter-test

  branches:
    main:
      - step: *setup-apple-signin
      - step: *flutter-test
      - step: *flutter-build-android
      - step: *flutter-build-ios

    develop:
      - step: *flutter-test
      - step:
          name: Setup Apple Sign-In for Development
          caches:
            - bundler
          script:
            - cd fastlane && bundle install --path vendor/bundle
            - bundle exec fastlane ios setup_apple_signin environment:development
            - bundle exec fastlane setup_android_apple_signin
            - bundle exec fastlane setup_web_apple_signin
      - step:
          name: Build Development APK
          caches:
            - flutter
          script:
            - flutter --version
            - flutter pub get
            - flutter build apk --flavor development --target lib/main_development.dart
          artifacts:
            - build/app/outputs/flutter-apk/*.apk

    staging:
      - step: *flutter-test
      - step:
          name: Setup Apple Sign-In for Staging
          caches:
            - bundler
          script:
            - cd fastlane && bundle install --path vendor/bundle
            - bundle exec fastlane ios setup_apple_signin environment:staging
            - bundle exec fastlane setup_android_apple_signin
            - bundle exec fastlane setup_web_apple_signin
      - step:
          name: Build Staging APK
          caches:
            - flutter
          script:
            - flutter --version
            - flutter pub get
            - flutter build apk --flavor staging --target lib/main_staging.dart
          artifacts:
            - build/app/outputs/flutter-apk/*.apk

  pull-requests:
    "**":
      - step: *flutter-test

  tags:
    "v*":
      - step: *flutter-test
      - step: *flutter-build-android
      - step: *flutter-build-ios
      - step:
          name: Create Release
          script:
            - echo "Creating release for tag $BITBUCKET_TAG"
            # Add deployment scripts here if needed
